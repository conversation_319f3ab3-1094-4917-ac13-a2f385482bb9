{"parser": "@typescript-eslint/parser", "env": {"browser": true, "commonjs": true, "es6": true, "node": true}, "extends": ["airbnb-typescript", "plugin:react/recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended", "prettier", "plugin:@next/next/recommended"], "parserOptions": {"project": ["tsconfig.json"], "ecmaVersion": 2020, "ecmaFeatures": {"jsx": true}, "sourceType": "module"}, "plugins": ["@typescript-eslint", "eslint-plugin-import", "prettier", "unused-imports", "react", "react-hooks"], "settings": {"react": {"version": "detect"}}, "rules": {"react/no-array-index-key": "off", "@typescript-eslint/ban-ts-comment": "off", "react/display-name": "off", "jsx-a11y/anchor-is-valid": "off", "react/require-default-props": "off", "react/jsx-no-duplicate-props": "off", "react/jsx-props-no-spreading": "off", "import/no-mutable-exports": "off", "@typescript-eslint/indent": "off", "@typescript-eslint/no-use-before-define": "off", "react/react-in-jsx-scope": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "react/prop-types": "off", "class-methods-use-this": "off", "import/no-cycle": "off", "import/prefer-default-export": "off", "no-console": ["warn", {"allow": ["info", "warn", "error"]}], "linebreak-style": "off", "arrow-parens": ["warn", "as-needed"], "@typescript-eslint/ban-types": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "unused-imports/no-unused-imports": "error", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}], "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "prettier/prettier": ["error", {"singleQuote": true, "trailingComma": "all", "printWidth": 80, "endOfLine": "auto"}], "sort-imports": ["error", {"ignoreCase": false, "ignoreDeclarationSort": true, "ignoreMemberSort": false, "memberSyntaxSortOrder": ["none", "all", "multiple", "single"]}]}}