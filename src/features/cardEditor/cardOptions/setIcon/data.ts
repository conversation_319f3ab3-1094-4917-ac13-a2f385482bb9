import { SetIcon } from '@cardEditor/cardOptions/setIcon';
import { swordAndShield as ssBaseSet } from '../baseSet';

export const swordAndShield: SetIcon = {
  id: 1,
  slug: 'swordAndShield',
  displayName: 'Base',
  baseSet: ssBaseSet.id,
};

export const rebelClash: SetIcon = {
  id: 2,
  slug: 'rebelClash',
  displayName: 'Rebel Clash',
  baseSet: ssBaseSet.id,
};

export const darknessAblaze: SetIcon = {
  id: 3,
  slug: 'darknessAblaze',
  displayName: 'Darkness Ablaze',
  baseSet: ssBaseSet.id,
};

export const championsPath: SetIcon = {
  id: 4,
  slug: 'championsPath',
  displayName: 'Champions Path',
  baseSet: ssBaseSet.id,
};

export const vividVoltage: SetIcon = {
  id: 5,
  slug: 'vividVoltage',
  displayName: 'Vivid Voltage',
  baseSet: ssBaseSet.id,
};

export const shiningFates: SetIcon = {
  id: 6,
  slug: 'shiningFates',
  displayName: 'Shining Fates',
  baseSet: ssBaseSet.id,
};

export const battleStyles: SetIcon = {
  id: 7,
  slug: 'battleStyles',
  displayName: 'Battle Styles',
  baseSet: ssBaseSet.id,
};

export const chillingReign: SetIcon = {
  id: 8,
  slug: 'chillingReign',
  displayName: 'Chilling Reign',
  baseSet: ssBaseSet.id,
};

export const evolvingSkies: SetIcon = {
  id: 9,
  slug: 'evolvingSkies',
  displayName: 'Evolving Skies',
  baseSet: ssBaseSet.id,
};

export const fusionStrike: SetIcon = {
  id: 10,
  slug: 'fusionStrike',
  displayName: 'Fusion Strike',
  baseSet: ssBaseSet.id,
};

export const brilliantStars: SetIcon = {
  id: 11,
  slug: 'brilliantStars',
  displayName: 'Brilliant Stars',
  baseSet: ssBaseSet.id,
};

export const promo: SetIcon = {
  id: 12,
  slug: 'promo',
  displayName: 'Promo',
  baseSet: ssBaseSet.id,
};

export const setIcons: SetIcon[] = [
  swordAndShield,
  rebelClash,
  darknessAblaze,
  championsPath,
  vividVoltage,
  shiningFates,
  battleStyles,
  chillingReign,
  evolvingSkies,
  fusionStrike,
  brilliantStars,
  promo,
];
