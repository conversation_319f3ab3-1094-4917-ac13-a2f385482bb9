import { RotationIcon } from '@cardEditor/cardOptions/rotationIcon';

export const ditto: RotationIcon = {
  id: 1,
  slug: 'ditto',
  displayName: 'Ditto',
  shape: 'square',
};

export const d: RotationIcon = {
  id: 2,
  slug: 'd',
  displayName: 'D',
  shape: 'rectangle',
};

export const e: RotationIcon = {
  id: 3,
  slug: 'e',
  displayName: 'E',
  shape: 'rectangle',
};

export const f: RotationIcon = {
  id: 4,
  slug: 'f',
  displayName: 'F',
  shape: 'rectangle',
};

export const rotationIcons: RotationIcon[] = [ditto, d, e, f];
